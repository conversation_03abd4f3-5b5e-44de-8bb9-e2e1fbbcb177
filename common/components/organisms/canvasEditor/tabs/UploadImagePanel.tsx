'use client'

import React, { useState } from 'react';
import Konva from 'konva';
import { Image as ImageIcon } from 'lucide-react';
import {
  acceptedImageMimeTypes, FILE_SIZE_10_MB,
} from '@/common/constants';
import toast from 'react-hot-toast';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';

interface UploadImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}

const scaleImageToFitCanvas = (
  img: Konva.Image,
  canvas: Konva.Stage,
  containerRef?: React.RefObject<HTMLDivElement>,
  zoomLevel?: number,
) => {

  if (containerRef?.current && zoomLevel) {
    const canvasWidth = canvas.width();
    const canvasHeight = canvas.height();
    const container = containerRef.current!;
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    const scaleX = containerWidth / canvasWidth;
    const scaleY = containerHeight / canvasHeight;

    const scale = Math.min(scaleX + zoomLevel, scaleY + .1 + zoomLevel + .1, 1.3);

    img.x(0);
    img.y(0);
    img.scaleX(scale);
    img.scaleY(scale);
    img.draggable(true);

    let layer = canvas.findOne('Layer') as Konva.Layer;
    if (!layer) {
      layer = new Konva.Layer();
      canvas.add(layer);
    }

    layer.add(img);

    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }

    transformer.nodes([img]);
    canvas.batchDraw();
  }
};

export const UploadImagePanel = ({
  canvas, agentId, planId, containerRef, zoomLevel,
}: UploadImagePanelProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const { activeProject } = useProjectContext();

  const validateFile = (file: File): string | null => {

    if (!acceptedImageMimeTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, GIF, SVG, or WebP)';
    }

    if (file.size > FILE_SIZE_10_MB) {
      return 'File size must be less than 10MB';
    }

    return null;
  };

  const handleFileUpload = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      return;
    }

    setIsUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imgUrl = event.target?.result as string;
        if (canvas) {
          const imageObj = new window.Image();
          imageObj.onload = () => {
            const konvaImage = new Konva.Image({
              image: imageObj,
            });
            scaleImageToFitCanvas(konvaImage, canvas, containerRef, zoomLevel);

            if (activeProject?.project_id && agentId) {
              projectImageStorage.addUploadedImage(
                activeProject.project_id,
                agentId,
                imgUrl,
                file.name,
                planId,
              ).then(() => {
                window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
              }).catch((error) => {
                console.error('Error storing uploaded image:', error);
              });
            }

            toast.success('Image added to canvas!');
          };
          imageObj.src = imgUrl;
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Import Image</h3>
        <p className="text-gray-400 text-sm">Add images from your device</p>
      </div>
      <div className="space-y-4">
        <div
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`border-2 border-dashed rounded-xl p-8 text-center transition-all cursor-pointer ${
            isDragging
              ? 'border-violets-are-blue bg-violets-are-blue/10'
              : 'border-neutral-600 hover:border-violets-are-blue'
          } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <ImageIcon className="mx-auto mb-3 text-gray-400" size={40} />
          <p className="text-gray-400 text-sm mb-2">
            {isDragging ? 'Drop image here' : 'Drag & drop or click to upload'}
          </p>
          <p className="text-gray-500 text-xs">PNG, JPG, SVG, GIF, WebP up to 10MB</p>
          {isUploading && (
            <div className="mt-3">
              <div className="text-violets-are-blue text-sm">Uploading...</div>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileInputChange}
          disabled={isUploading}
        />

        <div className="text-xs text-gray-500">
          <p>• Supported formats: JPEG, PNG, GIF, WebP</p>
          <p>• Maximum file size: 10MB</p>
        </div>
      </div>
    </div>
  );
};
